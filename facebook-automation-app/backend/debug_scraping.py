#!/usr/bin/env python3
"""
Debug script to test Facebook scraping with real UID extraction
"""
import asyncio
import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.append(str(Path(__file__).parent))

from app.utils.facebook_extractor import facebook_extractor
from app.utils.data_processor import data_processor
from app.models import ScrapingType

async def test_comment_extraction():
    """Test comment data extraction with new UID logic."""
    print("🧪 Testing Facebook Comment Extraction with Real UID Logic")
    print("=" * 60)
    
    # Mock page object (in real scenario this would be a browser page)
    mock_page = "mock_page"
    
    # Mock comment elements (in real scenario these would be DOM elements)
    mock_elements = [
        f"comment_element_{i}" for i in range(3)
    ]
    
    print(f"📋 Processing {len(mock_elements)} mock comment elements...")
    
    # Extract comment data using new logic
    extracted_comments = []
    for i, element in enumerate(mock_elements):
        print(f"\n🔍 Processing element {i+1}...")
        
        comment_data = await facebook_extractor._extract_comment_data(mock_page, element)
        if comment_data:
            extracted_comments.append(comment_data)
            print(f"✅ Extracted: UID={comment_data['uid']}, Name={comment_data['name']}")
        else:
            print("❌ Failed to extract comment data")
    
    print(f"\n📊 Extraction Results:")
    print(f"   - Total elements processed: {len(mock_elements)}")
    print(f"   - Successfully extracted: {len(extracted_comments)}")
    
    if extracted_comments:
        print(f"\n📋 Sample extracted data:")
        for i, data in enumerate(extracted_comments):
            print(f"   {i+1}. UID: {data['uid']}")
            print(f"      Name: {data['name']}")
            print(f"      URL: {data['profile_url']}")
            print(f"      Comment: {data['comment_text'][:50]}...")
            print(f"      Gender: {data['gender']}")
            print()
    
    # Test data processing
    print("🔄 Testing Data Processing Pipeline...")
    print("-" * 40)
    
    if extracted_comments:
        processed_data, stats = await data_processor.process_scraped_data(
            extracted_comments,
            validation_level="strict",
            deduplication_strategy="uid_priority"
        )
        
        print(f"📊 Processing Results:")
        print(f"   - Input records: {stats['input_count']}")
        print(f"   - Validation passed: {stats['validation_passed']}")
        print(f"   - Validation failed: {stats['validation_failed']}")
        print(f"   - Final count: {stats['final_count']}")
        
        if stats['validation_errors']:
            print(f"   - Validation errors: {stats['validation_errors'][:3]}...")
        
        if processed_data:
            print(f"\n✅ Successfully processed {len(processed_data)} records")
            print("📋 Sample processed data:")
            for i, data in enumerate(processed_data[:2]):
                print(f"   {i+1}. UID: {data['uid']}, Name: {data['name']}, Valid: ✅")
        else:
            print("❌ No data passed processing pipeline")
    else:
        print("❌ No data to process")

if __name__ == "__main__":
    asyncio.run(test_comment_extraction())
