"""
Enhanced Facebook scraper service using facebook-scraper library for accurate data extraction.
"""
import re
import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any, Set
from urllib.parse import urlparse, parse_qs
import logging

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from loguru import logger

# Import facebook-scraper library
import sys
import os
sys.path.append('/Users/<USER>/Documents/Projects/bot-follow/facebook-scraper')
import facebook_scraper as fs

from ..models.scraped_data import ScrapingSession, ScrapedUser, ScrapingType, Gender
from ..models.profile import Profile
from ..core.config import settings


class EnhancedFacebookScraperService:
    """Enhanced Facebook scraper using facebook-scraper library."""
    
    def __init__(self):
        self.extracted_uids: Set[str] = set()
        self.gender_patterns = self._load_gender_patterns()
        
    def _load_gender_patterns(self) -> Dict[str, List[str]]:
        """Load gender detection patterns."""
        return {
            "male": [
                # Vietnamese male names
                "anh", "bình", "cường", "dũng", "hải", "hùng", "khang", "long",
                "minh", "nam", "phong", "quang", "sơn", "th<PERSON>nh", "tuấn", "vi<PERSON>t",
                # English male names  
                "james", "robert", "john", "michael", "david", "william", "richard",
                "charles", "joseph", "thomas", "christopher", "daniel", "paul"
            ],
            "female": [
                # Vietnamese female names
                "châu", "diệu", "giang", "hà", "hằng", "hương", "lan",
                "linh", "mai", "nga", "ngọc", "nhung", "phương", "thảo", "thúy",
                "trang", "uyên", "vân", "yến",
                # English female names
                "mary", "patricia", "jennifer", "linda", "elizabeth", "barbara",
                "susan", "jessica", "sarah", "karen", "nancy", "lisa", "betty"
            ]
        }
    
    def reset_extraction_state(self):
        """Reset extraction state for new session."""
        self.extracted_uids.clear()
        logger.info("Reset extraction state")
    
    async def scrape_post_comments(
        self,
        post_url: str,
        max_comments: int = 1000,
        cookies_file: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Scrape comments from Facebook post using facebook-scraper library.
        
        Args:
            post_url: Facebook post URL
            max_comments: Maximum number of comments to extract
            cookies_file: Path to cookies file for authentication
            
        Returns:
            List of extracted user data from comments
        """
        logger.info(f"Starting comment scraping for URL: {post_url}")
        logger.info(f"Max comments: {max_comments}")
        
        extracted_users = []
        
        try:
            # Extract post ID from URL
            post_id = self._extract_post_id_from_url(post_url)
            if not post_id:
                logger.error(f"Could not extract post ID from URL: {post_url}")
                return extracted_users
            
            logger.info(f"Extracted post ID: {post_id}")
            
            # Configure facebook-scraper options
            options = {
                "comments": max_comments,
                "progress": True,
                "allow_extra_requests": True
            }
            
            # Set cookies if provided
            if cookies_file and os.path.exists(cookies_file):
                logger.info(f"Using cookies from: {cookies_file}")
                
            # Get post with comments using facebook-scraper
            logger.info("Fetching post data with facebook-scraper...")
            posts_generator = fs.get_posts(
                post_urls=[post_id],
                options=options,
                cookies=cookies_file if cookies_file and os.path.exists(cookies_file) else None
            )
            
            # Process the post
            for post in posts_generator:
                logger.info(f"Processing post: {post.get('post_id', 'Unknown')}")
                
                # Extract comments
                comments_full = post.get('comments_full', [])
                if not comments_full:
                    logger.warning("No comments found in post")
                    continue
                
                logger.info(f"Found {len(comments_full)} comments")
                
                # Process each comment
                for comment in comments_full:
                    user_data = self._process_comment_data(comment)
                    if user_data and user_data['uid'] not in self.extracted_uids:
                        extracted_users.append(user_data)
                        self.extracted_uids.add(user_data['uid'])
                        
                        # Process replies
                        replies = comment.get('replies', [])
                        for reply in replies:
                            reply_user_data = self._process_comment_data(reply)
                            if reply_user_data and reply_user_data['uid'] not in self.extracted_uids:
                                extracted_users.append(reply_user_data)
                                self.extracted_uids.add(reply_user_data['uid'])
                
                break  # We only process the first (and only) post
            
            logger.info(f"Successfully extracted {len(extracted_users)} unique users from comments")
            return extracted_users
            
        except Exception as e:
            logger.error(f"Error scraping comments: {e}")
            return extracted_users
    
    def _extract_post_id_from_url(self, url: str) -> Optional[str]:
        """Extract post ID from Facebook URL."""
        if not url:
            return None
            
        # Common Facebook URL patterns for post ID extraction
        patterns = [
            # Pattern 1: /posts/123456789
            r'/posts/(\d+)',
            # Pattern 2: story_fbid=123456789
            r'story_fbid=(\d+)',
            # Pattern 3: /permalink/123456789
            r'/permalink/(\d+)',
            # Pattern 4: fbid=123456789 (for photos)
            r'fbid=(\d+)',
            # Pattern 5: Direct post ID in URL
            r'facebook\.com/.*?(\d{10,})',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                post_id = match.group(1)
                logger.debug(f"Extracted post ID {post_id} from URL: {url}")
                return post_id
        
        logger.warning(f"Could not extract post ID from URL: {url}")
        return None
    
    def _process_comment_data(self, comment: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Process comment data from facebook-scraper into our format with deduplication."""
        try:
            # Extract basic info with enhanced text processing
            commenter_name = self._clean_text(comment.get('commenter_name', ''))
            commenter_url = comment.get('commenter_url', '')
            comment_text = self._extract_clean_comment_text(comment)

            if not commenter_name or not commenter_url:
                logger.debug("Skipping comment with missing name or URL")
                return None

            # Extract UID from URL
            uid = self._extract_uid_from_url(commenter_url)
            if not uid:
                logger.debug(f"Could not extract UID from URL: {commenter_url}")
                return None

            # Check for duplicates using enhanced deduplication
            if self._is_duplicate_user(uid, commenter_name, commenter_url):
                logger.debug(f"Skipping duplicate user: UID={uid}, Name={commenter_name}")
                return None

            # Add to extracted UIDs set for deduplication
            self.extracted_uids.add(uid)

            # Detect gender
            gender = self._detect_gender(commenter_name)

            # Clean profile URL
            clean_profile_url = self._normalize_facebook_url(commenter_url)

            user_data = {
                'uid': uid,
                'name': commenter_name,
                'profile_url': clean_profile_url,
                'interaction_type': ScrapingType.COMMENTS,
                'comment_text': comment_text,
                'gender': gender,
                'scraped_at': datetime.utcnow()
            }

            logger.debug(f"✅ Processed unique comment data: UID={uid}, Name={commenter_name}, Gender={gender}")
            return user_data

        except Exception as e:
            logger.error(f"Error processing comment data: {e}")
            return None

    def _is_duplicate_user(self, uid: str, name: str, url: str) -> bool:
        """Enhanced duplicate detection using multiple criteria."""
        # Primary check: UID already extracted
        if uid in self.extracted_uids:
            return True

        # Secondary check: Normalize and compare URLs
        normalized_url = self._normalize_facebook_url(url)
        for existing_uid in self.extracted_uids:
            # If we have stored normalized URLs, we could compare them here
            # For now, we rely on UID uniqueness
            pass

        # Tertiary check: Name similarity (for edge cases where UID extraction might vary)
        # This is more complex and might not be needed if UID extraction is robust

        return False
    
    def _extract_uid_from_url(self, url: str) -> Optional[str]:
        """Extract UID from Facebook URL using enhanced patterns from facebook-scraper."""
        if not url:
            return None

        logger.debug(f"Extracting UID from URL: {url}")

        # Enhanced UID extraction patterns based on facebook-scraper analysis
        patterns = [
            # Pattern 1: /user/123456789 (groups) - highest priority
            r'/user/(\d+)',
            # Pattern 2: /profile.php?id=123456789 - standard profile URLs
            r'profile\.php\?id=(\d+)',
            # Pattern 3: /people/Name/123456789 - people directory
            r'/people/[^/]+/(\d+)',
            # Pattern 4: fbid parameter in URLs
            r'[?&]fbid=(\d+)',
            # Pattern 5: id parameter in query string
            r'[?&]id=(\d+)',
            # Pattern 6: Direct numeric ID after facebook.com/
            r'facebook\.com/(\d{10,})(?:[/?&#]|$)',
            # Pattern 7: Numeric ID in path segments
            r'/(\d{10,})(?:[/?&#]|$)',
            # Pattern 8: From facebook-scraper commenter_id extraction
            r'feed_story_ring(\d+)',
            # Pattern 9: Entity ID pattern from facebook-scraper
            r'entity_id[:\s]*(\d+)',
        ]

        for i, pattern in enumerate(patterns, 1):
            match = re.search(pattern, url)
            if match:
                uid = match.group(1)
                # Validate UID (Facebook UIDs are typically 10+ digits)
                if len(uid) >= 10 and uid.isdigit():
                    logger.debug(f"✅ Extracted UID {uid} from URL using pattern {i}: {url}")
                    return uid
                elif len(uid) >= 8 and uid.isdigit():
                    # Some older Facebook IDs might be shorter
                    logger.debug(f"✅ Extracted shorter UID {uid} from URL using pattern {i}: {url}")
                    return uid

        # If no numeric UID found, try to extract username as fallback
        username_patterns = [
            # Pattern: facebook.com/username (not a system path)
            r'facebook\.com/([a-zA-Z0-9._-]+)(?:[/?&#]|$)',
            # Pattern: after domain, before query/fragment
            r'\.com/([a-zA-Z0-9._-]+)(?:[/?&#]|$)',
        ]

        for i, pattern in enumerate(username_patterns, 1):
            match = re.search(pattern, url)
            if match:
                username = match.group(1)
                # Skip common non-username paths and system paths
                skip_paths = [
                    'profile.php', 'people', 'pages', 'groups', 'events',
                    'photo.php', 'video.php', 'watch', 'story.php', 'posts',
                    'www', 'm', 'mbasic', 'web', 'login', 'help', 'about',
                    'privacy', 'terms', 'support', 'business', 'developers'
                ]

                # Additional validation for username
                if (username not in skip_paths and
                    not username.startswith('photo') and
                    not username.startswith('video') and
                    len(username) >= 3 and
                    not username.isdigit()):  # Pure numbers should be caught by UID patterns

                    logger.debug(f"✅ Extracted username {username} from URL using pattern {i}: {url}")
                    return username

        logger.warning(f"❌ Could not extract UID from URL: {url}")
        return None
    
    def _detect_gender(self, name: str) -> Gender:
        """Detect gender from name using pattern matching."""
        if not name:
            return Gender.UNKNOWN

        name_lower = name.lower()
        name_parts = name_lower.split()

        # Check for exact word matches first (more accurate)
        for part in name_parts:
            # Check female patterns first for exact matches
            if part in self.gender_patterns["female"]:
                return Gender.FEMALE
            # Check male patterns for exact matches
            if part in self.gender_patterns["male"]:
                return Gender.MALE

        # Fallback to substring matching
        for pattern in self.gender_patterns["female"]:
            if pattern in name_lower:
                return Gender.FEMALE

        for pattern in self.gender_patterns["male"]:
            if pattern in name_lower:
                return Gender.MALE

        return Gender.UNKNOWN
    
    def _normalize_facebook_url(self, url: str) -> str:
        """Normalize Facebook URL to standard format."""
        if not url:
            return ""
        if url == "None":
            return "None"
        
        # Convert mobile URLs to standard format
        url = url.replace('m.facebook.com', 'www.facebook.com')
        url = url.replace('mbasic.facebook.com', 'www.facebook.com')
        
        # Remove unnecessary parameters
        parsed = urlparse(url)
        if 'profile.php' in parsed.path:
            # Keep only id parameter for profile URLs
            query_params = parse_qs(parsed.query)
            if 'id' in query_params:
                return f"https://www.facebook.com/profile.php?id={query_params['id'][0]}"
        
        return url

    def _extract_clean_comment_text(self, comment: Dict[str, Any]) -> str:
        """Extract and clean comment text with proper handling of special characters."""
        comment_text = comment.get('comment_text', '')

        if not comment_text:
            return ''

        # Clean and normalize text
        cleaned_text = self._clean_text(comment_text)

        # Handle special cases from Facebook comments
        cleaned_text = self._handle_facebook_text_artifacts(cleaned_text)

        return cleaned_text

    def _clean_text(self, text: str) -> str:
        """Clean text while preserving emojis and special characters."""
        if not text:
            return ''

        # Strip whitespace but preserve internal spacing
        text = text.strip()

        # Handle Facebook text artifacts first
        text = self._handle_facebook_text_artifacts(text)

        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)

        # Remove null characters and control characters (except newlines and tabs)
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)

        return text.strip()

    def _handle_facebook_text_artifacts(self, text: str) -> str:
        """Handle Facebook-specific text artifacts and encoding issues."""
        if not text:
            return text

        # Handle common Facebook text artifacts
        replacements = {
            # HTML entities that might slip through
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'",
            '&nbsp;': ' ',

            # Facebook-specific artifacts
            '\u00a0': ' ',  # Non-breaking space
            '\u200b': '',   # Zero-width space
            '\u200c': '',   # Zero-width non-joiner
            '\u200d': '',   # Zero-width joiner
            '\ufeff': '',   # Byte order mark
        }

        for old, new in replacements.items():
            text = text.replace(old, new)

        # Clean up multiple spaces again after replacements
        text = re.sub(r'\s+', ' ', text).strip()

        return text


# Global instance
enhanced_facebook_scraper_service = EnhancedFacebookScraperService()
