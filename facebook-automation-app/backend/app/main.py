"""
FastAPI main application optimized for desktop app performance.
"""
import async<PERSON>
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from loguru import logger

from .core import settings, init_database, close_database


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager for startup and shutdown."""
    # Startup
    logger.info("Starting Facebook Automation App Backend...")
    
    # Initialize database
    await init_database()
    logger.info("Database initialized")
    
    # Setup logging
    if settings.log_file:
        logger.add(settings.log_file, rotation="10 MB", retention="7 days")
    
    logger.info(f"Server starting on {settings.api_host}:{settings.api_port}")
    
    yield
    
    # Shutdown
    logger.info("Shutting down...")
    await close_database()
    logger.info("Database connections closed")


# Create FastAPI app with optimizations
app = FastAPI(
    title=settings.app_name,
    version=settings.version,
    description="Backend API for Facebook Automation Desktop App",
    lifespan=lifespan,
    # Optimize for desktop app
    docs_url="/docs" if settings.debug else None,
    redoc_url="/redoc" if settings.debug else None,
)

# CORS middleware for frontend
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "tauri://localhost",
        "http://localhost:1420",  # Tauri default ports
        "http://localhost:3000",  # React development server
        "http://127.0.0.1:3000"   # React development server alternative
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint for health check."""
    return {
        "app": settings.app_name,
        "version": settings.version,
        "status": "running"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    from .core.database import db_manager
    
    db_healthy = await db_manager.health_check()
    
    return {
        "status": "healthy" if db_healthy else "unhealthy",
        "database": "connected" if db_healthy else "disconnected",
        "version": settings.version
    }


# Include API routers
from .api import profiles, scraping

# Import additional routers from the api directory (sync versions)
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), ".."))
from api import campaigns_router, analytics_router

app.include_router(profiles.router, prefix="/api/profiles", tags=["profiles"])
app.include_router(scraping.router, prefix="/api/scraping", tags=["scraping"])
app.include_router(campaigns_router, tags=["campaigns"])
app.include_router(analytics_router, tags=["analytics"])
# app.include_router(messaging.router, prefix="/api/messaging", tags=["messaging"])  # Will be added later


if __name__ == "__main__":
    import uvicorn
    
    # Run with single worker for desktop app
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        workers=1,  # Single worker for desktop app
        reload=settings.debug,
        log_level=settings.log_level.lower(),
        access_log=settings.debug,
    )
