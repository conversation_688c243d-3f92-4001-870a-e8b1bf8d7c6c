"""
Scraping API endpoints with enhanced Facebook scraping capabilities.
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from sqlalchemy.orm import selectinload
from pydantic import BaseModel, Field, HttpUrl
import logging
import re
from datetime import datetime
from urllib.parse import urlparse, parse_qs

from ..core.database import get_db
from ..models.scraped_data import ScrapingSession, ScrapedUser, ScrapingType, Gender
from ..services.enhanced_scraper_service import enhanced_facebook_scraper_service

logger = logging.getLogger(__name__)

router = APIRouter()


# Pydantic models
class ScrapingConfigCreate(BaseModel):
    name: str = Field(..., min_length=1, max_length=255)
    post_url: HttpUrl
    scraping_types: List[ScrapingType] = [ScrapingType.COMMENTS]
    max_users: Optional[int] = Field(None, ge=1, le=10000)
    include_comments: bool = True
    include_likes: bool = False
    include_shares: bool = False
    include_reactions: bool = False


class ScrapingSessionResponse(BaseModel):
    id: int
    name: str
    post_url: str
    scraping_types: str
    status: str
    total_found: int
    unique_users: int
    progress_percentage: float
    current_step: Optional[str]
    started_at: Optional[str]
    completed_at: Optional[str]
    error_message: Optional[str]
    
    class Config:
        from_attributes = True


class ScrapedUserResponse(BaseModel):
    id: int
    uid: str
    name: Optional[str]
    gender: Gender
    profile_url: Optional[str]
    interaction_type: ScrapingType
    comment_text: Optional[str]
    message_sent: bool
    scraped_at: str
    
    class Config:
        from_attributes = True


@router.get("/sessions", response_model=List[ScrapingSessionResponse])
async def get_scraping_sessions(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """Get list of scraping sessions."""
    query = select(ScrapingSession)
    
    if status:
        query = query.where(ScrapingSession.status == status)
    
    query = query.offset(skip).limit(limit).order_by(ScrapingSession.started_at.desc())
    
    result = await db.execute(query)
    sessions = result.scalars().all()
    
    # Format response
    response_sessions = []
    for session in sessions:
        session_dict = {
            "id": session.id,
            "name": session.name,
            "post_url": session.post_url,
            "scraping_types": session.scraping_types,
            "status": session.status,
            "total_found": session.total_found,
            "unique_users": session.unique_users,
            "progress_percentage": session.progress_percentage,
            "current_step": session.current_step,
            "started_at": session.started_at.isoformat() if session.started_at else None,
            "completed_at": session.completed_at.isoformat() if session.completed_at else None,
            "error_message": session.error_message
        }
        response_sessions.append(session_dict)
    
    return response_sessions


@router.get("/sessions/{session_id}", response_model=ScrapingSessionResponse)
async def get_scraping_session(session_id: int, db: AsyncSession = Depends(get_db)):
    """Get specific scraping session."""
    session = await db.get(ScrapingSession, session_id)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scraping session not found"
        )
    
    return {
        "id": session.id,
        "name": session.name,
        "post_url": session.post_url,
        "scraping_types": session.scraping_types,
        "status": session.status,
        "total_found": session.total_found,
        "unique_users": session.unique_users,
        "progress_percentage": session.progress_percentage,
        "current_step": session.current_step,
        "started_at": session.started_at.isoformat() if session.started_at else None,
        "completed_at": session.completed_at.isoformat() if session.completed_at else None,
        "error_message": session.error_message
    }


@router.get("/sessions/{session_id}/users", response_model=List[ScrapedUserResponse])
async def get_scraped_users(
    session_id: int,
    skip: int = 0,
    limit: int = 1000,
    interaction_type: Optional[ScrapingType] = None,
    gender: Optional[Gender] = None,
    message_sent: Optional[bool] = None,
    db: AsyncSession = Depends(get_db)
):
    """Get scraped users from session."""
    # Check if session exists
    session = await db.get(ScrapingSession, session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scraping session not found"
        )
    
    # Build query
    query = select(ScrapedUser).where(ScrapedUser.session_id == session_id)
    
    if interaction_type:
        query = query.where(ScrapedUser.interaction_type == interaction_type)
    
    if gender:
        query = query.where(ScrapedUser.gender == gender)
    
    if message_sent is not None:
        query = query.where(ScrapedUser.message_sent == message_sent)
    
    query = query.offset(skip).limit(limit).order_by(ScrapedUser.scraped_at.desc())
    
    result = await db.execute(query)
    users = result.scalars().all()
    
    # Format response
    response_users = []
    for user in users:
        user_dict = {
            "id": user.id,
            "uid": user.uid,
            "name": user.name,
            "gender": user.gender,
            "profile_url": user.profile_url,
            "interaction_type": user.interaction_type,
            "comment_text": user.comment_text,
            "message_sent": user.message_sent,
            "scraped_at": user.scraped_at.isoformat() if user.scraped_at else None
        }
        response_users.append(user_dict)
    
    return response_users


@router.post("/sessions", response_model=ScrapingSessionResponse, status_code=status.HTTP_201_CREATED)
async def create_scraping_session(
    config: ScrapingConfigCreate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """Create new scraping session."""
    import json
    
    # Prepare scraping types
    scraping_types = []
    if config.include_comments:
        scraping_types.append(ScrapingType.COMMENTS)
    if config.include_likes:
        scraping_types.append(ScrapingType.LIKES)
    if config.include_shares:
        scraping_types.append(ScrapingType.SHARES)
    if config.include_reactions:
        scraping_types.append(ScrapingType.REACTIONS)
    
    if not scraping_types:
        scraping_types = [ScrapingType.COMMENTS]  # Default
    
    # Create session
    session = ScrapingSession(
        name=config.name,
        post_url=str(config.post_url),
        scraping_types=json.dumps([t.value for t in scraping_types]),
        status="pending",
        total_found=0,
        unique_users=0,
        progress_percentage=0.0
    )
    
    db.add(session)
    await db.commit()
    await db.refresh(session)
    
    # TODO: Add background task to start scraping
    # background_tasks.add_task(start_scraping_task, session.id, config)
    
    return {
        "id": session.id,
        "name": session.name,
        "post_url": session.post_url,
        "scraping_types": session.scraping_types,
        "status": session.status,
        "total_found": session.total_found,
        "unique_users": session.unique_users,
        "progress_percentage": session.progress_percentage,
        "current_step": session.current_step,
        "started_at": session.started_at.isoformat() if session.started_at else None,
        "completed_at": session.completed_at.isoformat() if session.completed_at else None,
        "error_message": session.error_message
    }


@router.post("/sessions/{session_id}/start")
async def start_scraping(
    session_id: int,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """Start enhanced Facebook scraping for session."""
    from ..models.profile import Profile
    from loguru import logger
    import json

    session = await db.get(ScrapingSession, session_id)

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scraping session not found"
        )

    if session.status not in ["pending", "failed"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Session cannot be started in current status"
        )

    # Validate Facebook URL
    if not _is_valid_facebook_url(session.post_url):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid Facebook URL in session"
        )

    # Get a profile to use for scraping (for now, get the first available profile)
    # In a real implementation, this should be specified when creating the session
    result = await db.execute(select(Profile).limit(1))
    profile = result.scalar_one_or_none()

    if not profile:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No browser profile available for scraping"
        )

    logger.info(f"🚀 Starting enhanced scraping for session {session_id} with profile {profile.name}")

    # Start enhanced scraping in background
    background_tasks.add_task(
        _enhanced_scraping_task,
        session_id,
        session.post_url,
        json.loads(session.scraping_types),
        profile
    )

    return {"message": "Enhanced scraping started", "session_id": session_id, "profile_used": profile.name}


@router.post("/sessions/{session_id}/stop")
async def stop_scraping(session_id: int, db: AsyncSession = Depends(get_db)):
    """Stop scraping for session."""
    session = await db.get(ScrapingSession, session_id)

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scraping session not found"
        )

    if session.status != "running":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Session is not running"
        )

    # Update session status
    session.status = "cancelled"
    session.current_step = "Cancelled by user"
    await db.commit()

    return {"message": "Scraping stopped", "session_id": session_id}


@router.get("/sessions/{session_id}/users")
async def get_scraped_users(
    session_id: int,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db)
):
    """Get scraped users for a session."""
    session = await db.get(ScrapingSession, session_id)

    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scraping session not found"
        )

    # Get scraped users with pagination
    result = await db.execute(
        select(ScrapedUser)
        .where(ScrapedUser.session_id == session_id)
        .offset(skip)
        .limit(limit)
        .order_by(ScrapedUser.scraped_at.desc())
    )
    users = result.scalars().all()

    # Get total count
    count_result = await db.execute(
        select(func.count(ScrapedUser.id))
        .where(ScrapedUser.session_id == session_id)
    )
    total_count = count_result.scalar()

    return {
        "users": [
            {
                "id": user.id,
                "uid": user.uid,
                "name": user.name,
                "gender": user.gender.value if user.gender else None,
                "profile_url": user.profile_url,
                "interaction_type": user.interaction_type.value,
                "comment_text": user.comment_text,
                "scraped_at": user.scraped_at.isoformat(),
                "message_sent": user.message_sent
            }
            for user in users
        ],
        "total_count": total_count,
        "skip": skip,
        "limit": limit
    }


@router.delete("/sessions/{session_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_scraping_session(session_id: int, db: AsyncSession = Depends(get_db)):
    """Delete scraping session and all associated data."""
    session = await db.get(ScrapingSession, session_id)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scraping session not found"
        )
    
    if session.status == "running":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete running session"
        )
    
    await db.delete(session)
    await db.commit()


@router.get("/sessions/{session_id}/export")
async def export_scraped_data(
    session_id: int,
    format: str = "xlsx",
    db: AsyncSession = Depends(get_db)
):
    """Export scraped data to file."""
    from fastapi.responses import FileResponse
    from ..utils.data_export import data_exporter
    import os

    # Check if session exists
    session = await db.get(ScrapingSession, session_id)
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Scraping session not found"
        )

    # Get users
    result = await db.execute(
        select(ScrapedUser).where(ScrapedUser.session_id == session_id)
    )
    users = result.scalars().all()

    if not users:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No data to export"
        )

    try:
        # Export to file
        if format.lower() in ["xlsx", "excel"]:
            file_path = await data_exporter.export_scraped_users_to_excel(
                users,
                session_name=session.name
            )
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            filename = f"scraped_users_{session.name}_{session_id}.xlsx"
        elif format.lower() == "csv":
            file_path = await data_exporter.export_scraped_users_to_csv(
                users,
                session_name=session.name
            )
            media_type = "text/csv"
            filename = f"scraped_users_{session.name}_{session_id}.csv"
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported format. Use 'xlsx' or 'csv'"
            )

        # Return file response
        return FileResponse(
            path=str(file_path),
            media_type=media_type,
            filename=filename,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )

    except Exception as e:
        logger.error(f"Export failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Export failed: {str(e)}"
        )


# Helper functions
def _is_valid_facebook_url(url: str) -> bool:
    """Validate if URL is a valid Facebook URL."""
    try:
        parsed = urlparse(url)
        facebook_domains = [
            "facebook.com", "www.facebook.com", "m.facebook.com",
            "web.facebook.com", "mbasic.facebook.com"
        ]
        return parsed.netloc in facebook_domains
    except Exception:
        return False


async def _enhanced_scraping_task(
    session_id: int,
    post_url: str,
    scraping_types: List[str],
    profile: Any
):
    """Enhanced background scraping task using facebook-scraper library."""
    from ..core.database import get_async_session
    from loguru import logger
    import json

    logger.info(f"🔄 Starting enhanced scraping task for session {session_id}")

    async with get_async_session() as db:
        try:
            # Update session status
            session = await db.get(ScrapingSession, session_id)
            if not session:
                logger.error(f"Session {session_id} not found")
                return

            session.status = "running"
            session.current_step = "Initializing enhanced scraper"
            await db.commit()

            # Reset extraction state
            enhanced_facebook_scraper_service.reset_extraction_state()

            # Determine max comments based on scraping types
            max_comments = 1000  # Default
            if ScrapingType.COMMENTS.value in scraping_types:
                max_comments = 2000  # More comments if specifically requested

            session.current_step = f"Extracting comments (max: {max_comments})"
            await db.commit()

            # Use enhanced scraper to extract comments
            extracted_users = await enhanced_facebook_scraper_service.scrape_post_comments(
                post_url=post_url,
                max_comments=max_comments,
                cookies_file=None  # TODO: Use profile cookies
            )

            logger.info(f"📊 Extracted {len(extracted_users)} unique users")

            # Save extracted users to database
            session.current_step = f"Saving {len(extracted_users)} users to database"
            await db.commit()

            saved_count = 0
            for user_data in extracted_users:
                try:
                    scraped_user = ScrapedUser(
                        session_id=session_id,
                        uid=user_data['uid'],
                        name=user_data['name'],
                        profile_url=user_data['profile_url'],
                        interaction_type=user_data['interaction_type'],
                        comment_text=user_data.get('comment_text', ''),
                        gender=user_data['gender'],
                        scraped_at=user_data['scraped_at']
                    )
                    db.add(scraped_user)
                    saved_count += 1
                except Exception as e:
                    logger.error(f"Error saving user {user_data.get('uid', 'unknown')}: {e}")
                    continue

            await db.commit()

            # Update session with final results
            session.status = "completed"
            session.total_found = len(extracted_users)
            session.unique_users = saved_count
            session.progress_percentage = 100.0
            session.current_step = f"Completed - {saved_count} users saved"
            session.completed_at = datetime.utcnow()
            await db.commit()

            logger.info(f"✅ Enhanced scraping completed for session {session_id}: {saved_count} users saved")

        except Exception as e:
            logger.error(f"❌ Enhanced scraping failed for session {session_id}: {e}")

            # Update session with error
            try:
                session = await db.get(ScrapingSession, session_id)
                if session:
                    session.status = "failed"
                    session.error_message = str(e)
                    session.current_step = f"Failed: {str(e)}"
                    await db.commit()
            except Exception as db_error:
                logger.error(f"Failed to update session error status: {db_error}")
