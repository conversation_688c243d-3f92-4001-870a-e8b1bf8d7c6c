"""
Advanced Facebook data extraction engine with pagination and stealth techniques.
"""
import asyncio
import random
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple, Set
from enum import Enum

from loguru import logger

from ..models import ScrapingType, Gender


class FacebookElementSelectors:
    """CSS selectors for Facebook elements (updated for current FB structure)."""
    
    # Post selectors
    POST_CONTAINER = '[data-pagelet="FeedUnit_0"], [data-testid="fbfeed_story"]'
    POST_CONTENT = '[data-testid="post_message"], [data-ad-preview="message"]'
    
    # Comment selectors
    COMMENTS_CONTAINER = '[data-testid="UFI2Comment/root_depth_0"]'
    COMMENT_AUTHOR = '[data-testid="UFI2Comment/author_name"]'
    COMMENT_TEXT = '[data-testid="UFI2Comment/body"]'
    COMMENT_LINK = '[data-testid="UFI2Comment/author_name"] a'
    
    # Reaction selectors
    REACTIONS_BUTTON = '[data-testid="UFI2ReactionLink/root"]'
    REACTIONS_COUNT = '[data-testid="UFI2ReactionsCount/root"]'
    REACTIONS_POPUP = '[data-testid="UFI2ReactionsDialog/root"]'
    REACTION_USER = '[data-testid="UFI2ReactionsDialog/person"]'
    REACTION_USER_LINK = '[data-testid="UFI2ReactionsDialog/person"] a'

    # Share selectors
    SHARES_BUTTON = '[data-testid="UFI2SharesCount/root"]'
    SHARES_POPUP = '[data-testid="UFI2SharesDialog/root"]'
    SHARE_USER_LINK = '[data-testid="UFI2SharesDialog/person"] a'
    
    # Navigation selectors
    LOAD_MORE_COMMENTS = '[data-testid="UFI2Comment/load_more_button"]'
    LOAD_MORE_REACTIONS = '[data-testid="UFI2ReactionsDialog/load_more"]'
    
    # Profile selectors
    PROFILE_LINK = 'a[href*="/profile.php"], a[href*="facebook.com/"]'
    PROFILE_NAME = '[data-testid="profile_name"], .profileLink'


class UserDataExtractor:
    """
    Advanced user data extraction with intelligent parsing and validation.
    """
    
    def __init__(self):
        self.selectors = FacebookElementSelectors()
        self.extracted_uids: Set[str] = set()
        self.gender_patterns = self._load_gender_patterns()
    
    def _load_gender_patterns(self) -> Dict[str, List[str]]:
        """Load patterns for gender detection from names."""
        return {
            "male": [
                # Vietnamese male names
                "anh", "bình", "cường", "dũng", "đức", "giang", "hải", "hùng", 
                "khang", "long", "minh", "nam", "phong", "quang", "sơn", "thành",
                "tuấn", "việt", "vũ", "xuân",
                # English male names
                "john", "david", "michael", "james", "robert", "william", "richard",
                "thomas", "christopher", "daniel", "matthew", "anthony", "mark"
            ],
            "female": [
                # Vietnamese female names
                "anh", "châu", "diệu", "giang", "hà", "hằng", "hương", "lan",
                "linh", "mai", "nga", "ngọc", "nhung", "phương", "thảo", "thúy",
                "trang", "uyên", "vân", "yến",
                # English female names
                "mary", "patricia", "jennifer", "linda", "elizabeth", "barbara",
                "susan", "jessica", "sarah", "karen", "nancy", "lisa", "betty"
            ]
        }
    
    async def extract_comments(self, page, max_comments: int = 1000) -> List[Dict[str, Any]]:
        """Extract comments with pagination support."""
        logger.info(f"Starting comment extraction (max: {max_comments})")
        
        comments = []
        processed_count = 0
        
        try:
            # Scroll to load initial comments
            await self._scroll_to_load_content(page, "comments")
            
            # Load more comments if needed
            while processed_count < max_comments:
                # Find comment elements
                comment_elements = await self._find_elements(page, self.selectors.COMMENTS_CONTAINER)
                
                if not comment_elements:
                    logger.info("No comment elements found")
                    break
                
                # Process new comments
                new_comments = await self._process_comment_elements(page, comment_elements[processed_count:])
                comments.extend(new_comments)
                
                processed_count = len(comment_elements)
                
                if len(new_comments) == 0 or processed_count >= max_comments:
                    break
                
                # Try to load more comments
                load_more_success = await self._click_load_more(page, self.selectors.LOAD_MORE_COMMENTS)
                if not load_more_success:
                    break
                
                # Random delay to avoid detection
                await asyncio.sleep(random.uniform(1, 3))
            
            logger.info(f"Extracted {len(comments)} comments")
            return comments[:max_comments]
            
        except Exception as e:
            logger.error(f"Error extracting comments: {e}")
            return comments
    
    async def extract_reactions(self, page, max_reactions: int = 1000) -> List[Dict[str, Any]]:
        """Extract reactions/likes with pagination support."""
        logger.info(f"Starting reaction extraction (max: {max_reactions})")
        
        reactions = []
        
        try:
            # Click on reactions count to open popup
            reactions_button = await self._find_element(page, self.selectors.REACTIONS_BUTTON)
            if not reactions_button:
                logger.info("No reactions button found")
                return reactions
            
            await self._click_element(page, reactions_button)
            await asyncio.sleep(2)  # Wait for popup to load
            
            # Extract reactions from popup
            processed_count = 0
            while processed_count < max_reactions:
                reaction_elements = await self._find_elements(page, self.selectors.REACTION_USER)
                
                if not reaction_elements:
                    break
                
                # Process new reactions
                new_reactions = await self._process_reaction_elements(page, reaction_elements[processed_count:])
                reactions.extend(new_reactions)
                
                processed_count = len(reaction_elements)
                
                if len(new_reactions) == 0 or processed_count >= max_reactions:
                    break
                
                # Scroll in popup to load more
                await self._scroll_in_popup(page)
                await asyncio.sleep(random.uniform(1, 2))
            
            # Close popup
            await self._close_popup(page)
            
            logger.info(f"Extracted {len(reactions)} reactions")
            return reactions[:max_reactions]
            
        except Exception as e:
            logger.error(f"Error extracting reactions: {e}")
            return reactions
    
    async def extract_shares(self, page, max_shares: int = 500) -> List[Dict[str, Any]]:
        """Extract shares with pagination support."""
        logger.info(f"Starting share extraction (max: {max_shares})")
        
        shares = []
        
        try:
            # Click on shares count to open popup
            shares_button = await self._find_element(page, self.selectors.SHARES_BUTTON)
            if not shares_button:
                logger.info("No shares button found")
                return shares
            
            await self._click_element(page, shares_button)
            await asyncio.sleep(2)
            
            # Extract shares from popup (similar to reactions)
            processed_count = 0
            while processed_count < max_shares:
                share_elements = await self._find_elements(page, self.selectors.REACTION_USER)
                
                if not share_elements:
                    break
                
                new_shares = await self._process_share_elements(page, share_elements[processed_count:])
                shares.extend(new_shares)
                
                processed_count = len(share_elements)
                
                if len(new_shares) == 0 or processed_count >= max_shares:
                    break
                
                await self._scroll_in_popup(page)
                await asyncio.sleep(random.uniform(1, 2))
            
            await self._close_popup(page)
            
            logger.info(f"Extracted {len(shares)} shares")
            return shares[:max_shares]
            
        except Exception as e:
            logger.error(f"Error extracting shares: {e}")
            return shares
    
    async def _process_comment_elements(self, page, elements) -> List[Dict[str, Any]]:
        """Process comment elements to extract user data."""
        comments = []
        
        for element in elements:
            try:
                # Extract comment data
                comment_data = await self._extract_comment_data(page, element)
                if comment_data and comment_data["uid"] not in self.extracted_uids:
                    comments.append(comment_data)
                    self.extracted_uids.add(comment_data["uid"])
                    
            except Exception as e:
                logger.warning(f"Error processing comment element: {e}")
                continue
        
        return comments
    
    async def _process_reaction_elements(self, page, elements) -> List[Dict[str, Any]]:
        """Process reaction elements to extract user data."""
        reactions = []
        
        for element in elements:
            try:
                reaction_data = await self._extract_reaction_data(page, element)
                if reaction_data and reaction_data["uid"] not in self.extracted_uids:
                    reactions.append(reaction_data)
                    self.extracted_uids.add(reaction_data["uid"])
                    
            except Exception as e:
                logger.warning(f"Error processing reaction element: {e}")
                continue
        
        return reactions
    
    async def _process_share_elements(self, page, elements) -> List[Dict[str, Any]]:
        """Process share elements to extract user data."""
        shares = []
        
        for element in elements:
            try:
                share_data = await self._extract_share_data(page, element)
                if share_data and share_data["uid"] not in self.extracted_uids:
                    shares.append(share_data)
                    self.extracted_uids.add(share_data["uid"])
                    
            except Exception as e:
                logger.warning(f"Error processing share element: {e}")
                continue
        
        return shares
    
    async def _extract_comment_data(self, page, element) -> Optional[Dict[str, Any]]:
        """Extract data from a comment element."""
        try:
            logger.debug("🔍 Starting comment data extraction...")

            # Extract author name and profile link
            author_link_element = await self._find_element_in_parent(element, self.selectors.COMMENT_LINK)
            if not author_link_element:
                logger.warning("Could not find comment author link")
                return None

            # Get profile URL and extract UID
            profile_url = await self._get_element_attribute(author_link_element, "href")
            if not profile_url:
                logger.warning("Could not get profile URL from comment author")
                return None

            logger.debug(f"📍 Got profile URL: {profile_url}")

            # Extract UID from Facebook URL
            uid = self._extract_uid_from_url(profile_url)
            if not uid:
                logger.warning(f"Could not extract UID from URL: {profile_url}")
                return None

            logger.debug(f"🆔 Extracted UID: {uid}")

            # Extract author name
            name = await self._get_element_text(author_link_element)
            if not name:
                name = "Unknown User"

            logger.debug(f"👤 Extracted name: {name}")

            # Extract comment text
            comment_text_element = await self._find_element_in_parent(element, self.selectors.COMMENT_TEXT)
            comment_text = ""
            if comment_text_element:
                comment_text = await self._get_element_text(comment_text_element)

            logger.debug(f"💬 Extracted comment: {comment_text[:50]}..." if comment_text else "💬 No comment text")

            # Clean and normalize profile URL
            clean_profile_url = self._normalize_facebook_url(profile_url)

            # Detect gender
            gender = self._detect_gender(name)
            logger.debug(f"⚧️ Detected gender: {gender}")

            result = {
                "uid": uid,
                "name": name.strip(),
                "profile_url": clean_profile_url,
                "interaction_type": ScrapingType.COMMENTS,
                "comment_text": comment_text.strip() if comment_text else "",
                "gender": gender
            }

            logger.info(f"✅ Successfully extracted comment data: UID={uid}, Name={name}, Gender={gender}")
            return result

        except Exception as e:
            logger.error(f"Error extracting comment data: {e}")
            return None
    
    async def _extract_reaction_data(self, page, element) -> Optional[Dict[str, Any]]:
        """Extract data from a reaction element."""
        try:
            # Extract user link from reaction element
            user_link_element = await self._find_element_in_parent(element, self.selectors.REACTION_USER_LINK)
            if not user_link_element:
                logger.warning("Could not find reaction user link")
                return None

            # Get profile URL and extract UID
            profile_url = await self._get_element_attribute(user_link_element, "href")
            if not profile_url:
                logger.warning("Could not get profile URL from reaction user")
                return None

            # Extract UID from Facebook URL
            uid = self._extract_uid_from_url(profile_url)
            if not uid:
                logger.warning(f"Could not extract UID from URL: {profile_url}")
                return None

            # Extract user name
            name = await self._get_element_text(user_link_element)
            if not name:
                name = "Unknown User"

            # Clean and normalize profile URL
            clean_profile_url = self._normalize_facebook_url(profile_url)

            return {
                "uid": uid,
                "name": name.strip(),
                "profile_url": clean_profile_url,
                "interaction_type": ScrapingType.LIKES,
                "comment_text": None,
                "gender": self._detect_gender(name)
            }

        except Exception as e:
            logger.error(f"Error extracting reaction data: {e}")
            return None
    
    async def _extract_share_data(self, page, element) -> Optional[Dict[str, Any]]:
        """Extract data from a share element."""
        try:
            # Extract user link from share element
            user_link_element = await self._find_element_in_parent(element, self.selectors.SHARE_USER_LINK)
            if not user_link_element:
                logger.warning("Could not find share user link")
                return None

            # Get profile URL and extract UID
            profile_url = await self._get_element_attribute(user_link_element, "href")
            if not profile_url:
                logger.warning("Could not get profile URL from share user")
                return None

            # Extract UID from Facebook URL
            uid = self._extract_uid_from_url(profile_url)
            if not uid:
                logger.warning(f"Could not extract UID from URL: {profile_url}")
                return None

            # Extract user name
            name = await self._get_element_text(user_link_element)
            if not name:
                name = "Unknown User"

            # Clean and normalize profile URL
            clean_profile_url = self._normalize_facebook_url(profile_url)

            return {
                "uid": uid,
                "name": name.strip(),
                "profile_url": clean_profile_url,
                "interaction_type": ScrapingType.SHARES,
                "comment_text": None,
                "gender": self._detect_gender(name)
            }

        except Exception as e:
            logger.error(f"Error extracting share data: {e}")
            return None
    
    def _detect_gender(self, name: str) -> Gender:
        """Detect gender from name using pattern matching."""
        if not name:
            return Gender.UNKNOWN
        
        name_lower = name.lower()
        
        # Check for male patterns
        for pattern in self.gender_patterns["male"]:
            if pattern in name_lower:
                return Gender.MALE
        
        # Check for female patterns
        for pattern in self.gender_patterns["female"]:
            if pattern in name_lower:
                return Gender.FEMALE
        
        return Gender.UNKNOWN
    
    async def _scroll_to_load_content(self, page, content_type: str):
        """Scroll to load initial content."""
        # Mock implementation
        logger.info(f"Scrolling to load {content_type}")
        await asyncio.sleep(1)
    
    async def _click_load_more(self, page, selector: str) -> bool:
        """Click load more button if available."""
        # Mock implementation
        if random.random() < 0.7:  # 70% chance of having more content
            logger.info("Clicked load more button")
            await asyncio.sleep(random.uniform(1, 2))
            return True
        return False
    
    async def _scroll_in_popup(self, page):
        """Scroll within popup to load more content."""
        # Mock implementation
        await asyncio.sleep(0.5)
    
    async def _close_popup(self, page):
        """Close popup window."""
        # Mock implementation
        logger.info("Closed popup")
        await asyncio.sleep(0.5)
    
    async def _find_element(self, page, selector: str):
        """Find single element by selector."""
        # Mock implementation
        return f"element_{selector}" if random.random() > 0.1 else None
    
    async def _find_elements(self, page, selector: str):
        """Find multiple elements by selector."""
        # Mock implementation - return varying number of elements
        count = random.randint(5, 25)
        return [f"element_{selector}_{i}" for i in range(count)]
    
    async def _click_element(self, page, element):
        """Click on element with human-like behavior."""
        # Mock implementation
        await asyncio.sleep(random.uniform(0.1, 0.3))

    def _extract_uid_from_url(self, url: str) -> Optional[str]:
        """Extract UID from Facebook URL."""
        import re

        if not url:
            return None

        # Common Facebook URL patterns for UID extraction
        patterns = [
            # Pattern 1: /user/123456789 (groups)
            r'/user/(\d+)',
            # Pattern 2: /profile.php?id=123456789
            r'profile\.php\?id=(\d+)',
            # Pattern 3: /people/Name/123456789
            r'/people/[^/]+/(\d+)',
            # Pattern 4: Direct numeric ID in URL
            r'facebook\.com/(\d+)(?:/|$)',
            # Pattern 5: fbid parameter
            r'fbid=(\d+)',
            # Pattern 6: id parameter in URL
            r'[?&]id=(\d+)',
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                uid = match.group(1)
                logger.debug(f"Extracted UID {uid} from URL: {url}")
                return uid

        # If no numeric UID found, try to extract username
        username_patterns = [
            # Pattern: facebook.com/username
            r'facebook\.com/([^/?&#]+)',
            # Pattern: /username (after domain)
            r'\.com/([^/?&#]+)',
        ]

        for pattern in username_patterns:
            match = re.search(pattern, url)
            if match:
                username = match.group(1)
                # Skip common non-username paths
                skip_paths = ['profile.php', 'people', 'pages', 'groups', 'events', 'photo.php', 'video.php']
                if username not in skip_paths and not username.startswith('photo'):
                    logger.debug(f"Extracted username {username} from URL: {url}")
                    return username

        logger.warning(f"Could not extract UID from URL: {url}")
        return None

    def _normalize_facebook_url(self, url: str) -> str:
        """Normalize Facebook URL to standard format."""
        if not url:
            return ""

        # Remove tracking parameters and normalize
        import re

        # Remove common tracking parameters
        url = re.sub(r'[?&](ref|fref|hc_ref|__tn__|__xts__|hash)=[^&]*', '', url)

        # Ensure https
        if url.startswith('http://'):
            url = url.replace('http://', 'https://')
        elif not url.startswith('https://'):
            url = 'https://facebook.com/' + url.lstrip('/')

        # Clean up multiple slashes and trailing parameters (but preserve https://)
        # First ensure proper https:// format
        if url.startswith('https:/') and not url.startswith('https://'):
            url = url.replace('https:/', 'https://')

        # Clean up multiple slashes in path (but not in https://)
        parts = url.split('://', 1)
        if len(parts) == 2:
            protocol, rest = parts
            rest = re.sub(r'/+', '/', rest)
            url = f"{protocol}://{rest}"

        url = url.rstrip('?&')

        return url

    async def _find_element_in_parent(self, parent_element, selector: str):
        """Find element within parent element."""
        try:
            # This would be implemented with actual browser automation
            # For now, return mock element for testing
            return f"mock_element_{selector}"
        except Exception as e:
            logger.error(f"Error finding element {selector} in parent: {e}")
            return None

    async def _get_element_attribute(self, element, attribute: str) -> Optional[str]:
        """Get attribute value from element."""
        try:
            # Mock implementation - in real scenario, get from DOM
            if attribute == "href":
                # Return mock Facebook URLs with different UID patterns for testing
                patterns = [
                    "https://www.facebook.com/groups/591054007361950/user/1836579734",
                    "https://www.facebook.com/profile.php?id=100012345678",
                    "https://www.facebook.com/john.doe.123",
                    "https://www.facebook.com/people/Nguyen-Van-A/100087654321",
                ]
                return random.choice(patterns)
            return None
        except Exception as e:
            logger.error(f"Error getting attribute {attribute}: {e}")
            return None

    async def _get_element_text(self, element) -> Optional[str]:
        """Get text content from element."""
        try:
            # Mock implementation - in real scenario, get from DOM
            element_str = str(element)

            if "author" in element_str or "COMMENT_LINK" in element_str:
                # Return realistic Vietnamese names
                first_names = ["Nguyen", "Tran", "Le", "Pham", "Hoang", "Phan", "Vu", "Dang", "Bui", "Do"]
                last_names = ["Van", "Thi", "Duc", "Minh", "Anh", "Huy", "Linh", "Quan", "Thao", "Mai"]
                return f"{random.choice(first_names)} {random.choice(last_names)}"
            elif "UFI2Comment/body" in element_str or "COMMENT_TEXT" in element_str:
                # Return realistic comment text
                comments = [
                    "Cảm ơn bạn đã chia sẻ!",
                    "Thông tin rất hữu ích",
                    "Mình cũng đang quan tâm vấn đề này",
                    "Bạn có thể chia sẻ thêm không?",
                    "Rất hay và bổ ích",
                    "Thanks for sharing!",
                    "Very interesting post",
                    "I agree with your point"
                ]
                return random.choice(comments)
            return ""
        except Exception as e:
            logger.error(f"Error getting element text: {e}")
            return ""

    def reset_extraction_state(self):
        """Reset extraction state for new session."""
        self.extracted_uids.clear()
        logger.info("Reset extraction state")


# Global extractor instance
facebook_extractor = UserDataExtractor()
