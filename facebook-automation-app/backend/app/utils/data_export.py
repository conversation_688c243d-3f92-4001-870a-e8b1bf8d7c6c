"""
Data export utilities optimized for performance.
"""
import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

import pandas as pd
from loguru import logger

from ..core.config import settings
from ..models import ScrapedUser, ScrapingSession


class DataExporter:
    """
    High-performance data exporter with multiple format support.
    """
    
    def __init__(self):
        self.exports_dir = settings.exports_dir
    
    async def export_scraped_users_to_excel(
        self, 
        users: List[ScrapedUser], 
        filename: Optional[str] = None,
        session_name: Optional[str] = None
    ) -> Path:
        """
        Export scraped users to Excel with optimized performance.
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_part = f"_{session_name}" if session_name else ""
            filename = f"scraped_users{session_part}_{timestamp}.xlsx"
        
        filepath = self.exports_dir / filename
        
        # Convert to DataFrame efficiently
        data = []
        for i, user in enumerate(users, 1):
            data.append({
                "STT": i,
                "UID": user.uid,
                "Họ và tên": user.name or "N/A",
                "Giới tính": self._format_gender(user.gender),
                "Link Facebook": user.profile_url or "N/A",
                "Loại tương tác": self._format_interaction_type(user.interaction_type),
                "Nội dung comment": user.comment_text or "N/A",
                "Đã gửi tin nhắn": "Có" if user.message_sent else "Chưa",
                "Ngày scrape": user.scraped_at.strftime("%d/%m/%Y %H:%M:%S") if user.scraped_at else "N/A"
            })
        
        # Create DataFrame
        df = pd.DataFrame(data)
        
        # Export to Excel with formatting
        await asyncio.get_event_loop().run_in_executor(
            None, 
            self._write_excel_file, 
            df, 
            filepath
        )
        
        logger.info(f"Exported {len(users)} users to {filepath}")
        return filepath

    async def export_scraped_users_to_csv(
        self,
        users: List[ScrapedUser],
        filename: Optional[str] = None,
        session_name: Optional[str] = None
    ) -> Path:
        """
        Export scraped users to CSV format.
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            session_part = f"_{session_name}" if session_name else ""
            filename = f"scraped_users{session_part}_{timestamp}.csv"

        filepath = self.exports_dir / filename

        # Convert to DataFrame efficiently
        data = []
        for i, user in enumerate(users, 1):
            data.append({
                "STT": i,
                "UID": user.uid,
                "Họ và tên": user.name or "N/A",
                "Giới tính": self._format_gender(user.gender),
                "Link Facebook": user.profile_url or "N/A",
                "Loại tương tác": self._format_interaction_type(user.interaction_type),
                "Nội dung comment": user.comment_text or "N/A",
                "Đã gửi tin nhắn": "Có" if user.message_sent else "Chưa",
                "Ngày scrape": user.scraped_at.strftime("%d/%m/%Y %H:%M:%S") if user.scraped_at else "N/A"
            })

        # Create DataFrame and export to CSV
        df = pd.DataFrame(data)
        await asyncio.get_event_loop().run_in_executor(
            None,
            lambda: df.to_csv(filepath, index=False, encoding='utf-8')
        )

        logger.info(f"Exported {len(users)} users to CSV: {filepath}")
        return filepath

    def _write_excel_file(self, df: pd.DataFrame, filepath: Path):
        """Write DataFrame to Excel with formatting (runs in thread)."""
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Scraped Users', index=False)
            
            # Get workbook and worksheet
            workbook = writer.book
            worksheet = writer.sheets['Scraped Users']
            
            # Auto-adjust column widths
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                
                adjusted_width = min(max_length + 2, 50)  # Max width 50
                worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # Add header formatting
            from openpyxl.styles import Font, PatternFill, Alignment
            
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
    
    async def export_session_summary(
        self, 
        session: ScrapingSession, 
        users: List[ScrapedUser],
        filename: Optional[str] = None
    ) -> Path:
        """Export session summary with statistics."""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"session_summary_{session.id}_{timestamp}.xlsx"
        
        filepath = self.exports_dir / filename
        
        # Prepare summary data
        summary_data = {
            "Thông tin phiên scrape": [
                ["Tên phiên", session.name],
                ["URL bài viết", session.post_url],
                ["Trạng thái", session.status],
                ["Bắt đầu", session.started_at.strftime("%d/%m/%Y %H:%M:%S") if session.started_at else "N/A"],
                ["Hoàn thành", session.completed_at.strftime("%d/%m/%Y %H:%M:%S") if session.completed_at else "N/A"],
                ["Tổng số tìm thấy", session.total_found],
                ["Số user duy nhất", session.unique_users],
            ]
        }
        
        # Statistics by interaction type
        interaction_stats = {}
        gender_stats = {}
        
        for user in users:
            # Interaction type stats
            interaction_type = str(user.interaction_type)
            interaction_stats[interaction_type] = interaction_stats.get(interaction_type, 0) + 1
            
            # Gender stats
            gender = str(user.gender)
            gender_stats[gender] = gender_stats.get(gender, 0) + 1
        
        # Create DataFrames
        summary_df = pd.DataFrame(summary_data["Thông tin phiên scrape"], columns=["Thông tin", "Giá trị"])
        
        interaction_df = pd.DataFrame([
            {"Loại tương tác": k, "Số lượng": v} 
            for k, v in interaction_stats.items()
        ])
        
        gender_df = pd.DataFrame([
            {"Giới tính": self._format_gender(k), "Số lượng": v} 
            for k, v in gender_stats.items()
        ])
        
        # Users data
        users_data = []
        for i, user in enumerate(users, 1):
            users_data.append({
                "STT": i,
                "UID": user.uid,
                "Họ và tên": user.name or "N/A",
                "Giới tính": self._format_gender(user.gender),
                "Link Facebook": user.profile_url or "N/A",
                "Loại tương tác": self._format_interaction_type(user.interaction_type),
                "Đã gửi tin nhắn": "Có" if user.message_sent else "Chưa",
            })
        
        users_df = pd.DataFrame(users_data)
        
        # Export to Excel with multiple sheets
        await asyncio.get_event_loop().run_in_executor(
            None, 
            self._write_summary_excel, 
            filepath, 
            summary_df, 
            interaction_df, 
            gender_df, 
            users_df
        )
        
        logger.info(f"Exported session summary to {filepath}")
        return filepath
    
    def _write_summary_excel(
        self, 
        filepath: Path, 
        summary_df: pd.DataFrame,
        interaction_df: pd.DataFrame,
        gender_df: pd.DataFrame,
        users_df: pd.DataFrame
    ):
        """Write summary Excel file (runs in thread)."""
        with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
            # Write sheets
            summary_df.to_excel(writer, sheet_name='Tổng quan', index=False)
            interaction_df.to_excel(writer, sheet_name='Thống kê tương tác', index=False)
            gender_df.to_excel(writer, sheet_name='Thống kê giới tính', index=False)
            users_df.to_excel(writer, sheet_name='Danh sách users', index=False)
            
            # Format all sheets
            for sheet_name in writer.sheets:
                worksheet = writer.sheets[sheet_name]
                self._format_worksheet(worksheet)
    
    def _format_worksheet(self, worksheet):
        """Apply formatting to worksheet."""
        from openpyxl.styles import Font, PatternFill, Alignment
        
        # Auto-adjust column widths
        for column in worksheet.columns:
            max_length = 0
            column_letter = column[0].column_letter
            
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            
            adjusted_width = min(max_length + 2, 50)
            worksheet.column_dimensions[column_letter].width = adjusted_width
        
        # Header formatting
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center")
        
        for cell in worksheet[1]:
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
    
    def _format_gender(self, gender) -> str:
        """Format gender for display."""
        gender_map = {
            "male": "Nam",
            "female": "Nữ", 
            "unknown": "Không xác định"
        }
        return gender_map.get(str(gender).lower(), "Không xác định")
    
    def _format_interaction_type(self, interaction_type) -> str:
        """Format interaction type for display."""
        type_map = {
            "comments": "Bình luận",
            "likes": "Thích",
            "shares": "Chia sẻ",
            "reactions": "Cảm xúc"
        }
        return type_map.get(str(interaction_type).lower(), str(interaction_type))


# Global exporter instance
data_exporter = DataExporter()
