#!/usr/bin/env python3
"""
Test script for enhanced Facebook scraper service.
"""
import asyncio
import sys
import os
from datetime import datetime

# Add the app directory to Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'app'))

from app.services.enhanced_scraper_service import enhanced_facebook_scraper_service
from app.models.scraped_data import ScrapingType, Gender


def test_uid_extraction():
    """Test UID extraction from various Facebook URL patterns."""
    print("🧪 Testing UID extraction...")
    
    test_urls = [
        # Standard profile URLs
        ("https://www.facebook.com/profile.php?id=100012345678", "100012345678"),
        ("https://m.facebook.com/profile.php?id=100087654321", "100087654321"),
        
        # Group user URLs
        ("https://www.facebook.com/groups/591054007361950/user/1836579734", "1836579734"),
        ("https://m.facebook.com/groups/123456/user/9876543210", "9876543210"),
        
        # People directory URLs
        ("https://www.facebook.com/people/Nguyen-Van-A/100087654321", "100087654321"),
        ("https://facebook.com/people/<PERSON>-Doe/123456789012", "123456789012"),
        
        # Direct numeric IDs
        ("https://www.facebook.com/1234567890", "1234567890"),
        ("https://facebook.com/9876543210123", "9876543210123"),
        
        # Username URLs (should return username)
        ("https://www.facebook.com/john.doe.123", "john.doe.123"),
        ("https://facebook.com/nguyen.van.a", "nguyen.van.a"),
        
        # Invalid/edge cases
        ("https://www.facebook.com/profile.php", None),
        ("https://www.facebook.com/groups", None),
        ("https://www.google.com/", None),
        ("", None),
    ]
    
    passed = 0
    failed = 0
    
    for url, expected in test_urls:
        result = enhanced_facebook_scraper_service._extract_uid_from_url(url)
        if result == expected:
            print(f"✅ PASS: {url} -> {result}")
            passed += 1
        else:
            print(f"❌ FAIL: {url} -> Expected: {expected}, Got: {result}")
            failed += 1
    
    print(f"\n📊 UID Extraction Test Results: {passed} passed, {failed} failed")
    return failed == 0


def test_text_cleaning():
    """Test text cleaning and comment extraction."""
    print("\n🧪 Testing text cleaning...")
    
    test_texts = [
        # Basic text
        ("Hello world", "Hello world"),
        
        # Text with extra whitespace
        ("  Hello   world  ", "Hello world"),
        
        # Text with special characters and emojis
        ("Hello 👋 world! 🌍", "Hello 👋 world! 🌍"),
        
        # Text with HTML entities
        ("Hello &amp; world &lt;test&gt;", "Hello & world <test>"),
        
        # Text with Unicode artifacts
        ("Hello\u00a0world\u200b", "Hello world"),
        
        # Empty/null cases
        ("", ""),
        ("   ", ""),
        (None, ""),
    ]
    
    passed = 0
    failed = 0
    
    for input_text, expected in test_texts:
        if input_text is None:
            result = enhanced_facebook_scraper_service._clean_text("")
        else:
            result = enhanced_facebook_scraper_service._clean_text(input_text)
        
        if result == expected:
            print(f"✅ PASS: '{input_text}' -> '{result}'")
            passed += 1
        else:
            print(f"❌ FAIL: '{input_text}' -> Expected: '{expected}', Got: '{result}'")
            failed += 1
    
    print(f"\n📊 Text Cleaning Test Results: {passed} passed, {failed} failed")
    return failed == 0


def test_gender_detection():
    """Test gender detection from names."""
    print("\n🧪 Testing gender detection...")
    
    test_names = [
        # Vietnamese male names
        ("Nguyễn Văn Anh", Gender.MALE),
        ("Trần Minh Dũng", Gender.MALE),
        ("Lê Quang Hùng", Gender.MALE),
        
        # Vietnamese female names
        ("Nguyễn Thị Hương", Gender.FEMALE),
        ("Trần Thảo Linh", Gender.FEMALE),
        ("Lê Mai Phương", Gender.FEMALE),
        
        # English male names
        ("John Smith", Gender.MALE),
        ("Michael Johnson", Gender.MALE),
        ("David Brown", Gender.MALE),
        
        # English female names
        ("Mary Johnson", Gender.FEMALE),
        ("Jennifer Smith", Gender.FEMALE),
        ("Sarah Brown", Gender.FEMALE),
        
        # Ambiguous/unknown names
        ("Alex Taylor", Gender.UNKNOWN),
        ("Kim Nguyen", Gender.UNKNOWN),
        ("", Gender.UNKNOWN),
    ]
    
    passed = 0
    failed = 0
    
    for name, expected in test_names:
        result = enhanced_facebook_scraper_service._detect_gender(name)
        if result == expected:
            print(f"✅ PASS: '{name}' -> {result.value}")
            passed += 1
        else:
            print(f"❌ FAIL: '{name}' -> Expected: {expected.value}, Got: {result.value}")
            failed += 1
    
    print(f"\n📊 Gender Detection Test Results: {passed} passed, {failed} failed")
    return failed == 0


def test_url_normalization():
    """Test Facebook URL normalization."""
    print("\n🧪 Testing URL normalization...")
    
    test_urls = [
        # Mobile to standard conversion
        ("https://m.facebook.com/profile.php?id=123456", "https://www.facebook.com/profile.php?id=123456"),
        ("https://mbasic.facebook.com/john.doe", "https://www.facebook.com/john.doe"),
        
        # Already standard URLs
        ("https://www.facebook.com/profile.php?id=123456", "https://www.facebook.com/profile.php?id=123456"),
        ("https://facebook.com/john.doe", "https://facebook.com/john.doe"),
        
        # Empty/invalid URLs
        ("", ""),
        ("None", "None"),
    ]
    
    passed = 0
    failed = 0
    
    for input_url, expected in test_urls:
        result = enhanced_facebook_scraper_service._normalize_facebook_url(input_url)
        
        if result == expected:
            print(f"✅ PASS: '{input_url}' -> '{result}'")
            passed += 1
        else:
            print(f"❌ FAIL: '{input_url}' -> Expected: '{expected}', Got: '{result}'")
            failed += 1
    
    print(f"\n📊 URL Normalization Test Results: {passed} passed, {failed} failed")
    return failed == 0


def test_deduplication():
    """Test deduplication mechanism."""
    print("\n🧪 Testing deduplication...")
    
    # Reset extraction state
    enhanced_facebook_scraper_service.reset_extraction_state()
    
    # Test duplicate detection
    uid1 = "123456789"
    uid2 = "987654321"
    
    # First occurrence should not be duplicate
    is_dup1 = enhanced_facebook_scraper_service._is_duplicate_user(uid1, "User 1", "https://facebook.com/user1")
    enhanced_facebook_scraper_service.extracted_uids.add(uid1)
    
    # Second occurrence of same UID should be duplicate
    is_dup2 = enhanced_facebook_scraper_service._is_duplicate_user(uid1, "User 1 Again", "https://facebook.com/user1")
    
    # Different UID should not be duplicate
    is_dup3 = enhanced_facebook_scraper_service._is_duplicate_user(uid2, "User 2", "https://facebook.com/user2")
    
    passed = 0
    failed = 0
    
    if not is_dup1:
        print("✅ PASS: First occurrence not detected as duplicate")
        passed += 1
    else:
        print("❌ FAIL: First occurrence incorrectly detected as duplicate")
        failed += 1
    
    if is_dup2:
        print("✅ PASS: Second occurrence correctly detected as duplicate")
        passed += 1
    else:
        print("❌ FAIL: Second occurrence not detected as duplicate")
        failed += 1
    
    if not is_dup3:
        print("✅ PASS: Different UID not detected as duplicate")
        passed += 1
    else:
        print("❌ FAIL: Different UID incorrectly detected as duplicate")
        failed += 1
    
    print(f"\n📊 Deduplication Test Results: {passed} passed, {failed} failed")
    return failed == 0


async def test_comment_processing():
    """Test comment data processing."""
    print("\n🧪 Testing comment processing...")
    
    # Reset extraction state
    enhanced_facebook_scraper_service.reset_extraction_state()
    
    # Mock comment data similar to facebook-scraper output
    test_comments = [
        {
            "commenter_name": "Nguyễn Văn Anh",
            "commenter_url": "https://www.facebook.com/profile.php?id=100012345678",
            "comment_text": "Great post! 👍 Thanks for sharing."
        },
        {
            "commenter_name": "Mary Johnson",
            "commenter_url": "https://www.facebook.com/mary.johnson",
            "comment_text": "I love this &amp; want to share it!"
        },
        {
            "commenter_name": "",  # Invalid - missing name
            "commenter_url": "https://www.facebook.com/profile.php?id=123456",
            "comment_text": "This should be skipped"
        },
        {
            "commenter_name": "Duplicate User",
            "commenter_url": "https://www.facebook.com/profile.php?id=100012345678",  # Same UID as first
            "comment_text": "This should be skipped as duplicate"
        }
    ]
    
    processed_count = 0
    skipped_count = 0
    
    for i, comment in enumerate(test_comments):
        result = enhanced_facebook_scraper_service._process_comment_data(comment)
        if result:
            print(f"✅ Processed comment {i+1}: UID={result['uid']}, Name={result['name']}")
            processed_count += 1
        else:
            print(f"⏭️  Skipped comment {i+1}: {comment.get('commenter_name', 'No name')}")
            skipped_count += 1
    
    expected_processed = 2  # First two valid comments
    expected_skipped = 2    # Invalid and duplicate
    
    success = (processed_count == expected_processed and skipped_count == expected_skipped)
    
    print(f"\n📊 Comment Processing Results: {processed_count} processed, {skipped_count} skipped")
    print(f"Expected: {expected_processed} processed, {expected_skipped} skipped")
    
    return success


async def main():
    """Run all tests."""
    print("🚀 Starting Enhanced Facebook Scraper Tests\n")
    
    tests = [
        ("UID Extraction", test_uid_extraction),
        ("Text Cleaning", test_text_cleaning),
        ("Gender Detection", test_gender_detection),
        ("URL Normalization", test_url_normalization),
        ("Deduplication", test_deduplication),
        ("Comment Processing", test_comment_processing),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"Running {test_name} Test")
        print('='*50)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                print(f"✅ {test_name} Test: PASSED")
                passed_tests += 1
            else:
                print(f"❌ {test_name} Test: FAILED")
        except Exception as e:
            print(f"💥 {test_name} Test: ERROR - {e}")
    
    print(f"\n{'='*50}")
    print(f"🏁 FINAL RESULTS")
    print('='*50)
    print(f"Tests Passed: {passed_tests}/{total_tests}")
    print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    if passed_tests == total_tests:
        print("🎉 All tests passed! Enhanced scraper is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Please review and fix issues.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
