{"version": 3, "names": ["_checkPrivateRedeclaration", "require", "_classPrivateFieldInitSpec", "obj", "privateMap", "value", "checkPrivateRedeclaration", "set"], "sources": ["../../src/helpers/classPrivateFieldInitSpec.ts"], "sourcesContent": ["/* @minVersion 7.14.1 */\n\nimport checkPrivateRedeclaration from \"./checkPrivateRedeclaration.ts\";\n\nexport default function _classPrivateFieldInitSpec(\n  obj: object,\n  privateMap: WeakMap<object, unknown>,\n  value: unknown,\n) {\n  checkPrivateRedeclaration(obj, privateMap);\n  privateMap.set(obj, value);\n}\n"], "mappings": ";;;;;;AAEA,IAAAA,0BAAA,GAAAC,OAAA;AAEe,SAASC,0BAA0BA,CAChDC,GAAW,EACXC,UAAoC,EACpCC,KAAc,EACd;EACA,IAAAC,kCAAyB,EAACH,GAAG,EAAEC,UAAU,CAAC;EAC1CA,UAAU,CAACG,GAAG,CAACJ,GAAG,EAAEE,KAAK,CAAC;AAC5B", "ignoreList": []}