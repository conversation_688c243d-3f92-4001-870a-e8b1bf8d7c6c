{"version": 3, "file": "module-rebuilder.js", "sourceRoot": "", "sources": ["../src/module-rebuilder.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,kDAA0B;AAC1B,6CAA+B;AAC/B,2CAA6B;AAE7B,mCAA2C;AAC3C,8DAA0D;AAC1D,2DAAwD;AACxD,qEAAiE;AACjE,6DAAwD;AAGxD,MAAM,CAAC,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAC;AAEpC,MAAa,eAAe;IAQ1B,YAAY,SAAqB,EAAE,UAAkB;QACnD,IAAI,CAAC,UAAU,GAAG,UAAU,CAAC;QAC7B,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAE3B,IAAI,CAAC,OAAO,GAAG,IAAI,kBAAO,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QAClD,IAAI,CAAC,WAAW,GAAG,IAAI,yBAAW,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QAC1D,IAAI,CAAC,eAAe,GAAG,IAAI,kCAAe,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;QAClE,IAAI,CAAC,UAAU,GAAG,IAAI,yBAAU,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;IAC1D,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,aAAa,CAAC,CAAC;IACzF,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC;IACzD,CAAC;IAED,KAAK,CAAC,qBAAqB;QACzB,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;YACtC,MAAM,IAAI,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;YACtD,OAAO,IAAI,KAAK,IAAI,CAAC,QAAQ,CAAC;SAC/B;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAgB;QACrC,IAAI,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE;YAC3B,MAAM,IAAA,wBAAgB,EAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;SAC7E;IACH,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iCAAiC;QACrC,OAAO,IAAI,CAAC,eAAe,CAAC,oBAAoB,EAAE,CAAC;IACrD,CAAC;IAED;;;OAGG;IACH,KAAK,CAAC,qBAAqB,CAAC,QAAgB;QAC1C,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,EAAE;YACrC,CAAC,CAAC,oCAAoC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,CAAC,CAAC;YAErE,IAAI,MAAM,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,EAAE;gBAC/C,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC3B,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBACtC,OAAO,IAAI,CAAC;aACb;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,QAAgB;QAC9C,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,EAAE;YACzC,CAAC,CAAC,yCAAyC,IAAI,CAAC,eAAe,CAAC,UAAU,EAAE,CAAC,CAAC;YAE9E,IAAI,MAAM,IAAI,CAAC,eAAe,CAAC,kBAAkB,EAAE,EAAE;gBACnD,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,CAAC;gBACjE,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC3B,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBACtC,OAAO,IAAI,CAAC;aACb;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,QAAgB;QAChD,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,EAAE;YACpC,CAAC,CAAC,qCAAqC,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,CAAC,CAAC;YAErE,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,kBAAkB,EAAE,EAAE;gBAC9C,CAAC,CAAC,4BAA4B,EAAE,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC;gBAC5D,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;gBAC3B,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;gBACtC,OAAO,IAAI,CAAC;aACb;SACF;QAED,OAAO,KAAK,CAAC;IACf,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,QAAgB;QACzC,MAAM,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,CAAC;QACnC,CAAC,CAAC,qBAAqB,EAAE,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,CAAC;QAClD,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;QAC3B,MAAM,IAAI,CAAC,2BAA2B,EAAE,CAAC;QACzC,MAAM,IAAI,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC;QACtC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,KAAK,CAAC,2BAA2B;QAC/B,MAAM,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;QAEvF,CAAC,CAAC,0BAA0B,EAAE,aAAa,CAAC,CAAC;QAC7C,MAAM,kBAAkB,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC3D,CAAC,CAAC,eAAe,EAAE,kBAAkB,CAAC,CAAC;QAEvC,MAAM,QAAQ,GAAG,kBAAkB,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,KAAK,OAAO,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC;QAC/F,MAAM,QAAQ,GAAG,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,aAAa,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;QAE9E,IAAI,QAAQ,IAAI,MAAM,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC7C,CAAC,CAAC,kBAAkB,EAAE,QAAQ,CAAC,CAAC;YAChC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE;gBACrC,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,IAAI,CAAC,SAAS,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC7H,CAAC,CAAC,4BAA4B,EAAE,OAAO,CAAC,CAAC;gBACzC,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;gBAC7C,MAAM,EAAE,CAAC,QAAQ,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,OAAO,CAAC,CAAC,CAAC;aACpF;SACF;IACH,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,MAAM,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,CAAC,CAAC;IACpD,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,QAAgB;QAC5B,IACE,CAAC,IAAI,CAAC,SAAS,CAAC,eAAe,IAAI,CACjC,CAAC,MAAM,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;YAC5C,CAAC,MAAM,IAAI,CAAC,yBAAyB,CAAC,QAAQ,CAAC,CAAC;YAChD,CAAC,MAAM,IAAI,CAAC,2BAA2B,CAAC,QAAQ,CAAC,CAAC,CAAC,EACrD;YACA,OAAO,IAAI,CAAC;SACb;QAED,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,CAAC,CAAC;IACnD,CAAC;CACF;AA9ID,0CA8IC"}