{"name": "@electron/fuses", "version": "1.8.0", "main": "dist/index.js", "license": "MIT", "bin": {"electron-fuses": "dist/bin.js"}, "scripts": {"build": "tsc", "test": "jest", "prepublishOnly": "tsc", "prepare": "husky install"}, "devDependencies": {"@electron/get": "^2.0.1", "@electron/universal": "^1.3.0", "@types/fs-extra": "^9.0.1", "@types/jest": "^29.0.3", "@types/minimist": "^1.2.1", "@types/node": "^14.11.2", "extract-zip": "^2.0.1", "husky": "^8.0.0", "jest": "^29.0.3", "lint-staged": "^10.4.0", "prettier": "^2.1.2", "ts-jest": "^29.0.1", "typescript": "^4.3.3"}, "lint-staged": {"*.ts": ["prettier --write"]}, "dependencies": {"chalk": "^4.1.1", "fs-extra": "^9.0.1", "minimist": "^1.2.5"}, "files": ["dist", "README.md"], "description": "Flip Electron Fuses and customize your packaged build of Electron", "repository": {"type": "git", "url": "git+https://github.com/electron/fuses.git"}, "keywords": ["electron", "fuses"], "author": "Electron Community", "bugs": {"url": "https://github.com/electron/fuses/issues"}, "homepage": "https://github.com/electron/fuses#readme"}